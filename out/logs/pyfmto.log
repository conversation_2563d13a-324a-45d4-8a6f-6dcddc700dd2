
                               ____                __         
            ____     __  __   / __/  ____ ___     / /_   ____ 
           / __ \   / / / /  / /_   / __ `__ \   / __/  / __ \
          / /_/ /  / /_/ /  / __/  / / / / / /  / /_   / /_/ /
         / .___/   \__, /  /_/    /_/ /_/ /_/   \__/   \____/ 
        /_/       /____/                                      

INFO    2025-08-05 10:12:02          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:12:02          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:12:02          server.py->line(162)|Client 3 join, total 18 clients
ERROR   2025-08-05 10:12:03          client.py->line(205)|Client 6  Connection refused 1 times.
ERROR   2025-08-05 10:12:03          client.py->line(205)|Client 9  Connection refused 1 times.
ERROR   2025-08-05 10:12:03          client.py->line(205)|Client 7  Connection refused 1 times.
ERROR   2025-08-05 10:12:03          client.py->line(205)|Client 5  Connection refused 1 times.
ERROR   2025-08-05 10:12:03          client.py->line(205)|Client 14 Connection refused 1 times.
ERROR   2025-08-05 10:12:03          client.py->line(205)|Client 4  Connection refused 1 times.
ERROR   2025-08-05 10:12:03          client.py->line(205)|Client 15 Connection refused 1 times.
ERROR   2025-08-05 10:12:03          client.py->line(205)|Client 10 Connection refused 1 times.
ERROR   2025-08-05 10:12:03          client.py->line(205)|Client 12 Connection refused 1 times.
ERROR   2025-08-05 10:12:03          client.py->line(205)|Client 18 Connection refused 1 times.
ERROR   2025-08-05 10:12:03          client.py->line(205)|Client 11 Connection refused 1 times.
ERROR   2025-08-05 10:12:03          client.py->line(205)|Client 13 Connection refused 1 times.
ERROR   2025-08-05 10:12:03          client.py->line(205)|Client 17 Connection refused 1 times.
ERROR   2025-08-05 10:12:03          client.py->line(205)|Client 16 Connection refused 1 times.
ERROR   2025-08-05 10:12:03          client.py->line(205)|Client 8  Connection refused 1 times.
ERROR   2025-08-05 10:12:04          client.py->line(205)|Client 1  Connection refused 1 times.
ERROR   2025-08-05 10:12:04          client.py->line(205)|Client 2  Connection refused 1 times.
ERROR   2025-08-05 10:12:04          client.py->line(205)|Client 3  Connection refused 1 times.
ERROR   2025-08-05 10:12:04          client.py->line(205)|Client 6  Connection refused 2 times.
ERROR   2025-08-05 10:12:04          client.py->line(205)|Client 9  Connection refused 2 times.
ERROR   2025-08-05 10:12:04          client.py->line(205)|Client 7  Connection refused 2 times.
ERROR   2025-08-05 10:12:04          client.py->line(205)|Client 5  Connection refused 2 times.
ERROR   2025-08-05 10:12:04          client.py->line(205)|Client 14 Connection refused 2 times.
ERROR   2025-08-05 10:12:04          client.py->line(205)|Client 4  Connection refused 2 times.
ERROR   2025-08-05 10:12:04          client.py->line(205)|Client 15 Connection refused 2 times.
ERROR   2025-08-05 10:12:04          client.py->line(205)|Client 10 Connection refused 2 times.
ERROR   2025-08-05 10:12:04          client.py->line(205)|Client 12 Connection refused 2 times.
ERROR   2025-08-05 10:12:04          client.py->line(205)|Client 18 Connection refused 2 times.
ERROR   2025-08-05 10:12:04          client.py->line(205)|Client 11 Connection refused 2 times.
ERROR   2025-08-05 10:12:04          client.py->line(205)|Client 13 Connection refused 2 times.
ERROR   2025-08-05 10:12:04          client.py->line(205)|Client 17 Connection refused 2 times.
ERROR   2025-08-05 10:12:04          client.py->line(205)|Client 8  Connection refused 2 times.
ERROR   2025-08-05 10:12:04          client.py->line(205)|Client 16 Connection refused 2 times.
ERROR   2025-08-05 10:12:05          client.py->line(205)|Client 1  Connection refused 2 times.
ERROR   2025-08-05 10:12:05          client.py->line(205)|Client 2  Connection refused 2 times.
ERROR   2025-08-05 10:12:05          client.py->line(205)|Client 3  Connection refused 2 times.
ERROR   2025-08-05 10:12:05          client.py->line(205)|Client 6  Connection refused 3 times.
ERROR   2025-08-05 10:12:05          client.py->line(205)|Client 9  Connection refused 3 times.
ERROR   2025-08-05 10:12:05          client.py->line(205)|Client 7  Connection refused 3 times.
ERROR   2025-08-05 10:12:05          client.py->line(205)|Client 5  Connection refused 3 times.
ERROR   2025-08-05 10:12:05          client.py->line(205)|Client 14 Connection refused 3 times.
ERROR   2025-08-05 10:12:05          client.py->line(205)|Client 4  Connection refused 3 times.
ERROR   2025-08-05 10:12:05          client.py->line(205)|Client 15 Connection refused 3 times.
ERROR   2025-08-05 10:12:05          client.py->line(205)|Client 10 Connection refused 3 times.
ERROR   2025-08-05 10:12:05          client.py->line(205)|Client 12 Connection refused 3 times.
ERROR   2025-08-05 10:12:05          client.py->line(205)|Client 18 Connection refused 3 times.
ERROR   2025-08-05 10:12:05          client.py->line(205)|Client 11 Connection refused 3 times.
ERROR   2025-08-05 10:12:05          client.py->line(205)|Client 17 Connection refused 3 times.
ERROR   2025-08-05 10:12:05          client.py->line(205)|Client 13 Connection refused 3 times.
ERROR   2025-08-05 10:12:05          client.py->line(205)|Client 8  Connection refused 3 times.
ERROR   2025-08-05 10:12:05          client.py->line(205)|Client 16 Connection refused 3 times.
ERROR   2025-08-05 10:12:06          client.py->line(205)|Client 1  Connection refused 3 times.
ERROR   2025-08-05 10:12:06          client.py->line(205)|Client 2  Connection refused 3 times.
ERROR   2025-08-05 10:12:06          client.py->line(205)|Client 3  Connection refused 3 times.
ERROR   2025-08-05 10:12:06          client.py->line(205)|Client 6  Connection refused 1 times.
ERROR   2025-08-05 10:12:06          client.py->line(205)|Client 9  Connection refused 1 times.
ERROR   2025-08-05 10:12:06          client.py->line(205)|Client 7  Connection refused 1 times.
ERROR   2025-08-05 10:12:06          client.py->line(205)|Client 14 Connection refused 1 times.
ERROR   2025-08-05 10:12:06          client.py->line(205)|Client 5  Connection refused 1 times.
ERROR   2025-08-05 10:12:06          client.py->line(205)|Client 15 Connection refused 1 times.
ERROR   2025-08-05 10:12:06          client.py->line(205)|Client 4  Connection refused 1 times.
ERROR   2025-08-05 10:12:06          client.py->line(205)|Client 10 Connection refused 1 times.
ERROR   2025-08-05 10:12:06          client.py->line(205)|Client 12 Connection refused 1 times.
ERROR   2025-08-05 10:12:06          client.py->line(205)|Client 18 Connection refused 1 times.
ERROR   2025-08-05 10:12:06          client.py->line(205)|Client 11 Connection refused 1 times.
ERROR   2025-08-05 10:12:06          client.py->line(205)|Client 17 Connection refused 1 times.
ERROR   2025-08-05 10:12:06          client.py->line(205)|Client 13 Connection refused 1 times.
ERROR   2025-08-05 10:12:06          client.py->line(205)|Client 8  Connection refused 1 times.
ERROR   2025-08-05 10:12:06          client.py->line(205)|Client 16 Connection refused 1 times.
ERROR   2025-08-05 10:12:07          client.py->line(205)|Client 1  Connection refused 1 times.
ERROR   2025-08-05 10:12:07          client.py->line(205)|Client 2  Connection refused 1 times.
ERROR   2025-08-05 10:12:07          client.py->line(205)|Client 3  Connection refused 1 times.
ERROR   2025-08-05 10:12:07          client.py->line(205)|Client 6  Connection refused 2 times.
ERROR   2025-08-05 10:12:07          client.py->line(205)|Client 9  Connection refused 2 times.
ERROR   2025-08-05 10:12:07          client.py->line(205)|Client 14 Connection refused 2 times.
ERROR   2025-08-05 10:12:07          client.py->line(205)|Client 7  Connection refused 2 times.
ERROR   2025-08-05 10:12:07          client.py->line(205)|Client 5  Connection refused 2 times.
ERROR   2025-08-05 10:12:07          client.py->line(205)|Client 15 Connection refused 2 times.
ERROR   2025-08-05 10:12:07          client.py->line(205)|Client 4  Connection refused 2 times.
ERROR   2025-08-05 10:12:07          client.py->line(205)|Client 10 Connection refused 2 times.
ERROR   2025-08-05 10:12:07          client.py->line(205)|Client 12 Connection refused 2 times.
ERROR   2025-08-05 10:12:07          client.py->line(205)|Client 18 Connection refused 2 times.
ERROR   2025-08-05 10:12:07          client.py->line(205)|Client 11 Connection refused 2 times.
ERROR   2025-08-05 10:12:07          client.py->line(205)|Client 17 Connection refused 2 times.
ERROR   2025-08-05 10:12:07          client.py->line(205)|Client 13 Connection refused 2 times.
ERROR   2025-08-05 10:12:07          client.py->line(205)|Client 16 Connection refused 2 times.
ERROR   2025-08-05 10:12:07          client.py->line(205)|Client 8  Connection refused 2 times.
ERROR   2025-08-05 10:12:08          client.py->line(205)|Client 1  Connection refused 2 times.
ERROR   2025-08-05 10:12:08          client.py->line(205)|Client 2  Connection refused 2 times.
ERROR   2025-08-05 10:12:08          client.py->line(205)|Client 3  Connection refused 2 times.
INFO    2025-08-05 10:12:08           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 10:12:08           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 10:12:08           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 10:12:08           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 10:12:08           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 10:12:08           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 10:12:08           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 10:12:08           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 10:12:08           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 10:12:08           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 10:12:08           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 10:12:08           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 10:12:08           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 10:12:08           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 10:12:08           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 10:12:08           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 10:12:08           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 10:12:08           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 10:12:08        launcher.py->line(169)|
╭───────────┬──────────┬────────────────┬─────────────┬───────────┬───────┬───────────┬────────╮
│  running  │  repeat  │    progress    │  algorithm  │  problem  │  iid  │  clients  │  save  │
├───────────┼──────────┼────────────────┼─────────────┼───────────┼───────┼───────────┼────────┤
│    1/1    │   4/20   │ [4/20][20.00%] │   IAFFBO    │ arxiv2017 │   2   │    18     │  True  │
╰───────────┴──────────┴────────────────┴─────────────┴───────────┴───────┴───────────┴────────╯
INFO    2025-08-05 10:12:08        launcher.py->line(196)|Server started.
ERROR   2025-08-05 10:12:08          client.py->line(205)|Client 6  Connection refused 3 times.
ERROR   2025-08-05 10:12:08          client.py->line(205)|Client 9  Connection refused 3 times.
ERROR   2025-08-05 10:12:08          client.py->line(205)|Client 14 Connection refused 3 times.
ERROR   2025-08-05 10:12:08          client.py->line(205)|Client 7  Connection refused 3 times.
ERROR   2025-08-05 10:12:08          client.py->line(205)|Client 5  Connection refused 3 times.
ERROR   2025-08-05 10:12:08          client.py->line(205)|Client 15 Connection refused 3 times.
ERROR   2025-08-05 10:12:08          client.py->line(205)|Client 4  Connection refused 3 times.
ERROR   2025-08-05 10:12:08          client.py->line(205)|Client 10 Connection refused 3 times.
ERROR   2025-08-05 10:12:08          client.py->line(205)|Client 12 Connection refused 3 times.
ERROR   2025-08-05 10:12:08          client.py->line(205)|Client 18 Connection refused 3 times.
ERROR   2025-08-05 10:12:08          client.py->line(205)|Client 11 Connection refused 3 times.
ERROR   2025-08-05 10:12:08          client.py->line(205)|Client 17 Connection refused 3 times.
ERROR   2025-08-05 10:12:08          client.py->line(205)|Client 13 Connection refused 3 times.
ERROR   2025-08-05 10:12:08          client.py->line(205)|Client 8  Connection refused 3 times.
ERROR   2025-08-05 10:12:08          client.py->line(205)|Client 16 Connection refused 3 times.
ERROR   2025-08-05 10:12:09          client.py->line(205)|Client 1  Connection refused 3 times.
ERROR   2025-08-05 10:12:09          client.py->line(205)|Client 2  Connection refused 3 times.
ERROR   2025-08-05 10:12:09          client.py->line(205)|Client 3  Connection refused 3 times.
INFO    2025-08-05 10:12:10          client.py->line(111)|Client 1  started
INFO    2025-08-05 10:12:10          client.py->line(111)|Client 2  started
INFO    2025-08-05 10:12:10          client.py->line(111)|Client 3  started
INFO    2025-08-05 10:12:10          client.py->line(111)|Client 4  started
INFO    2025-08-05 10:12:10          client.py->line(111)|Client 5  started
INFO    2025-08-05 10:12:10          client.py->line(111)|Client 6  started
INFO    2025-08-05 10:12:10          client.py->line(111)|Client 7  started
INFO    2025-08-05 10:12:10          client.py->line(111)|Client 8  started
INFO    2025-08-05 10:12:10          client.py->line(111)|Client 9  started
INFO    2025-08-05 10:12:10          client.py->line(111)|Client 10 started
INFO    2025-08-05 10:12:10          client.py->line(111)|Client 11 started
INFO    2025-08-05 10:12:10          client.py->line(111)|Client 12 started
INFO    2025-08-05 10:12:10          client.py->line(111)|Client 13 started
INFO    2025-08-05 10:12:10          client.py->line(111)|Client 14 started
INFO    2025-08-05 10:12:10          client.py->line(111)|Client 15 started
INFO    2025-08-05 10:12:10          client.py->line(111)|Client 16 started
INFO    2025-08-05 10:12:10          client.py->line(111)|Client 17 started
INFO    2025-08-05 10:12:10          client.py->line(111)|Client 18 started
ERROR   2025-08-05 10:12:11          client.py->line(205)|Client 1  Connection refused 1 times.
ERROR   2025-08-05 10:12:11          client.py->line(205)|Client 3  Connection refused 1 times.
ERROR   2025-08-05 10:12:11          client.py->line(205)|Client 2  Connection refused 1 times.
ERROR   2025-08-05 10:12:11          client.py->line(205)|Client 4  Connection refused 1 times.
ERROR   2025-08-05 10:12:11          client.py->line(205)|Client 5  Connection refused 1 times.
ERROR   2025-08-05 10:12:11          client.py->line(205)|Client 6  Connection refused 1 times.
ERROR   2025-08-05 10:12:11          client.py->line(205)|Client 8  Connection refused 1 times.
ERROR   2025-08-05 10:12:11          client.py->line(205)|Client 9  Connection refused 1 times.
ERROR   2025-08-05 10:12:11          client.py->line(205)|Client 7  Connection refused 1 times.
ERROR   2025-08-05 10:12:11          client.py->line(205)|Client 10 Connection refused 1 times.
ERROR   2025-08-05 10:12:11          client.py->line(205)|Client 11 Connection refused 1 times.
ERROR   2025-08-05 10:12:11          client.py->line(205)|Client 12 Connection refused 1 times.
ERROR   2025-08-05 10:12:11          client.py->line(205)|Client 13 Connection refused 1 times.
ERROR   2025-08-05 10:12:11          client.py->line(205)|Client 14 Connection refused 1 times.
ERROR   2025-08-05 10:12:11          client.py->line(205)|Client 15 Connection refused 1 times.
ERROR   2025-08-05 10:12:11          client.py->line(205)|Client 16 Connection refused 1 times.
ERROR   2025-08-05 10:12:11          client.py->line(205)|Client 17 Connection refused 1 times.
ERROR   2025-08-05 10:12:11          client.py->line(205)|Client 18 Connection refused 1 times.
INFO    2025-08-05 10:12:11           tools.py->line(207)|
===================== IAFFBOServer ====================
╭───────────────────┬───────────┬───────────┬─────────╮
│ Parameter         │  Default  │  Updates  │  Using  │
├───────────────────┼───────────┼───────────┼─────────┤
│ device            │     -     │   cuda    │    -    │
├───────────────────┼───────────┼───────────┼─────────┤
│ batch_aggregation │     -     │   True    │    -    │
├───────────────────┼───────────┼───────────┼─────────┤
│ n_clusters        │     6     │     6     │    6    │
├───────────────────┼───────────┼───────────┼─────────┤
│ agg_interval      │    0.3    │    0.3    │   0.3   │
├───────────────────┼───────────┼───────────┼─────────┤
│ n_samples         │    100    │    100    │   100   │
╰───────────────────┴───────────┴───────────┴─────────╯
ERROR   2025-08-05 10:12:12          client.py->line(205)|Client 1  Connection refused 2 times.
INFO    2025-08-05 10:12:12          server.py->line(162)|Client 1 join, total 1 clients
ERROR   2025-08-05 10:12:12          client.py->line(205)|Client 3  Connection refused 2 times.
INFO    2025-08-05 10:12:12          server.py->line(162)|Client 3 join, total 2 clients
ERROR   2025-08-05 10:12:12          client.py->line(205)|Client 2  Connection refused 2 times.
ERROR   2025-08-05 10:12:12          client.py->line(205)|Client 4  Connection refused 2 times.
ERROR   2025-08-05 10:12:12          client.py->line(205)|Client 5  Connection refused 2 times.
ERROR   2025-08-05 10:12:12          client.py->line(205)|Client 6  Connection refused 2 times.
INFO    2025-08-05 10:12:12          server.py->line(162)|Client 2 join, total 3 clients
INFO    2025-08-05 10:12:12          server.py->line(162)|Client 4 join, total 4 clients
INFO    2025-08-05 10:12:12          server.py->line(162)|Client 6 join, total 5 clients
INFO    2025-08-05 10:12:12          server.py->line(162)|Client 5 join, total 6 clients
ERROR   2025-08-05 10:12:12          client.py->line(205)|Client 8  Connection refused 2 times.
INFO    2025-08-05 10:12:12          server.py->line(162)|Client 8 join, total 7 clients
ERROR   2025-08-05 10:12:12          client.py->line(205)|Client 9  Connection refused 2 times.
ERROR   2025-08-05 10:12:12          client.py->line(205)|Client 7  Connection refused 2 times.
ERROR   2025-08-05 10:12:12          client.py->line(205)|Client 10 Connection refused 2 times.
INFO    2025-08-05 10:12:12          server.py->line(162)|Client 9 join, total 8 clients
INFO    2025-08-05 10:12:12          server.py->line(162)|Client 7 join, total 9 clients
ERROR   2025-08-05 10:12:12          client.py->line(205)|Client 11 Connection refused 2 times.
ERROR   2025-08-05 10:12:12          client.py->line(205)|Client 13 Connection refused 2 times.
INFO    2025-08-05 10:12:12          server.py->line(162)|Client 10 join, total 10 clients
ERROR   2025-08-05 10:12:12          client.py->line(205)|Client 12 Connection refused 2 times.
INFO    2025-08-05 10:12:12          server.py->line(162)|Client 11 join, total 11 clients
INFO    2025-08-05 10:12:12          server.py->line(162)|Client 13 join, total 12 clients
INFO    2025-08-05 10:12:12          server.py->line(162)|Client 12 join, total 13 clients
ERROR   2025-08-05 10:12:12          client.py->line(205)|Client 14 Connection refused 2 times.
INFO    2025-08-05 10:12:12          server.py->line(162)|Client 14 join, total 14 clients
ERROR   2025-08-05 10:12:12          client.py->line(205)|Client 15 Connection refused 2 times.
ERROR   2025-08-05 10:12:12          client.py->line(205)|Client 17 Connection refused 2 times.
ERROR   2025-08-05 10:12:12          client.py->line(205)|Client 18 Connection refused 2 times.
INFO    2025-08-05 10:12:12          server.py->line(162)|Client 15 join, total 15 clients
ERROR   2025-08-05 10:12:12          client.py->line(205)|Client 16 Connection refused 2 times.
INFO    2025-08-05 10:12:12          server.py->line(162)|Client 17 join, total 16 clients
INFO    2025-08-05 10:12:12          server.py->line(162)|Client 18 join, total 17 clients
INFO    2025-08-05 10:12:12          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:12:13          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:12:13          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:12:13          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:12:13          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:12:13          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:12:13          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:12:13          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:12:13          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:12:13          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:12:13          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:12:13          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:12:13          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:12:13          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:12:13          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:12:13          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:12:13          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:12:13          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:12:13          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:12:14          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:12:14          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:12:14          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:12:14          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:12:14          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:12:14          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:12:14          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:12:14          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:12:14          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:12:14          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:12:14          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:12:14          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:12:14          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:12:14          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:12:14          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:12:14          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:12:14          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:12:14          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:12:15          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:12:15          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:12:15          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:12:15          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:12:15          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:12:15          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:12:15          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:12:15          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:12:15          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:12:15          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:12:15          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:12:15          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:12:15          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:12:15          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:12:15          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:12:15          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:12:15          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:12:15          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:12:16          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:12:16          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:12:16          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:12:16          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:12:16          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:12:16          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:12:16          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:12:16          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:12:16          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:12:16          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:12:16          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:12:16          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:12:16          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:12:16          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:12:16          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:12:16          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:12:16          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:12:16          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:12:17          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:12:17          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:12:17          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:12:17          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:12:17          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:12:17          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:12:17          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:12:17          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:12:17          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:12:17          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:12:17          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:12:17          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:12:17          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:12:17          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:12:17          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:12:17          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:12:17          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:12:17          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:12:18          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:12:18          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:12:18          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:12:18          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:12:18          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:12:18          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:12:18          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:12:18          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:12:18          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:12:18          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:12:18          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:12:18          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:12:18          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:12:18          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:12:18          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:12:18          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:12:18          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:12:18          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:12:19          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:12:19          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:12:19          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:12:19          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:12:19          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:12:19          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:12:19          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:12:19          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:12:19          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:12:19          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:12:19          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:12:19          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:12:19          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:12:19          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:12:19          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:12:19          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:12:19          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:12:19          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:12:20          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:12:20          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:12:20          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:12:20          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:12:20          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:12:20          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:12:20          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:12:20          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:12:20          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:12:20          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:12:20          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:12:20          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:12:20          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:12:20          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:12:20          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:12:20          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:12:20          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:12:20          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:12:21          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:12:21          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:12:21          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:12:21          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:12:21          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:12:21          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:12:21          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:12:21          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:12:21          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:12:21          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:12:21          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:12:21          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:12:21          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:12:21          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:12:21          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:12:21          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:12:21          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:12:21          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:12:22          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:12:22          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:12:22          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:12:22          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:12:22          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:12:22          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:12:22          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:12:22          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:12:22          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:12:22          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:12:22          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:12:22          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:12:22          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:12:22          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:12:22          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:12:22          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:12:22          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:12:22          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:12:23          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:12:23          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:12:23          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:12:23          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:12:23          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:12:23          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:12:23          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:12:23          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:12:23          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:12:23          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:12:23          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:12:23          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:12:23          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:12:23          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:12:23          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:12:23          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:12:23          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:12:23          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:12:24          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:12:24          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:12:24          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:12:24          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:12:24          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:12:24          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:12:24          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:12:24          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:12:24          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:12:24          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:12:24          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:12:24          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:12:24          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:12:24          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:12:24          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:12:24          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:12:24          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:12:24          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:12:25          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:12:25          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:12:25          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:12:25          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:12:25          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:12:25          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:12:25          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:12:25          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:12:25          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:12:25          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:12:25          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:12:25          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:12:25          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:12:25          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:12:25          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:12:25          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:12:25          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:12:25          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:12:26          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:12:26          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:12:26          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:12:26          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:12:26          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:12:26          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:12:26          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:12:26          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:12:26          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:12:26          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:12:26          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:12:26          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:12:26          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:12:26          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:12:26          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:12:26          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:12:26          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:12:26          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:12:27          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:12:27          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:12:27          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:12:27          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:12:27          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:12:27          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:12:27          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:12:27          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:12:27          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:12:27          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:12:27          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:12:27          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:12:27          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:12:27          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:12:27          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:12:27          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:12:27          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:12:27          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:12:28          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:12:28          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:12:28          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:12:28          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:12:28          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:12:28          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:12:28          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:12:28          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:12:28          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:12:28          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:12:28          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:12:28          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:12:28          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:12:28          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:12:28          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:12:28          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:12:28          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:12:28          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:12:29          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:12:29          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:12:29          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:12:29          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:12:29          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:12:29          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:12:29          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:12:29          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:12:29          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:12:29          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:12:29          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:12:29          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:12:29          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:12:29          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:12:29          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:12:29          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:12:29          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:12:29          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:12:30          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:12:30          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:12:30          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:12:30          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:12:30          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:12:30          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:12:30          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:12:30          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:12:30          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:12:30          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:12:30          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:12:30          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:12:30          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:12:30          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:12:30          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:12:30          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:12:30          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:12:30          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:12:31          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:12:31          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:12:31          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:12:31          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:12:31          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:12:31          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:12:31          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:12:31          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:12:31          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:12:31          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:12:31          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:12:31          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:12:31          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:12:31          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:12:31          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:12:31          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:12:31          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:12:31          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:12:32          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:12:32          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:12:32          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:12:32          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:12:32          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:12:32          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:12:32          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:12:32          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:12:32          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:12:32          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:12:32          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:12:32          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:12:32          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:12:32          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:12:32          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:12:32          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:12:32          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:12:32          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:12:33          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:12:33          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:12:33          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:12:33          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:12:33          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:12:33          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:12:33          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:12:33          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:12:33          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:12:33          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:12:33          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:12:33          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:12:33          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:12:33          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:12:33          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:12:33          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:12:33          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:12:33          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:12:34          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:12:34          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:12:34          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:12:34          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:12:34          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:12:34          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:12:34          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:12:34          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:12:34          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:12:34          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:12:34          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:12:34          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:12:34          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:12:34          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:12:34          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:12:34          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:12:34          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:12:34          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:12:35          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:12:35          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:12:35          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:12:35          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:12:35          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:12:35          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:12:35          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:12:35          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:12:35          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:12:35          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:12:35          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:12:35          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:12:35          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:12:35          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:12:35          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:12:35          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:12:35          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:12:35          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:12:36          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:12:36          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:12:36          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:12:36          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:12:36          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:12:36          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:12:36          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:12:36          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:12:36          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:12:36          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:12:36          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:12:36          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:12:36          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:12:36          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:12:36          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:12:36          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:12:36          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:12:36          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:12:37          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:12:37          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:12:37          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:12:37          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:12:37          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:12:37          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:12:37          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:12:37          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:12:37          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:12:37          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:12:37          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:12:37          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:12:37          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:12:37          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:12:37          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:12:37          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:12:37          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:12:37          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:12:38          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:12:38          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:12:38          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:12:38          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:12:38          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:12:38          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:12:38          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:12:38          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:12:38          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:12:38          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:12:38          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:12:38          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:12:38          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:12:38          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:12:38          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:12:38          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:12:38          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:12:38          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:12:39          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:12:39          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:12:39          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:12:39          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:12:39          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:12:39          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:12:39          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:12:39          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:12:39          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:12:39          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:12:39          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:12:39          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:12:39          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:12:39          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:12:39          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:12:39          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:12:39          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:12:39          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:12:40          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:12:40          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:12:40          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:12:40          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:12:40          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:12:40          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:12:40          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:12:40          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:12:40          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:12:40          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:12:40          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:12:40          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:12:40          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:12:40          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:12:40          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:12:40          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:12:40          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:12:40          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:12:41          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:12:41          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:12:41          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:12:41          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:12:41          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:12:41          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:12:41          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:12:41          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:12:41          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:12:41          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:12:41          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:12:41          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:12:41          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:12:41          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:12:41          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:12:41          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:12:41          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:12:41          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:12:42          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:12:42          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:12:42          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:12:42          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:12:42          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:12:42          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:12:42          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:12:42          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:12:42          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:12:42          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:12:42          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:12:42          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:12:42          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:12:42          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:12:42          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:12:42          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:12:42          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:12:42          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:12:43          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:12:43          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:12:43          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:12:43          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:12:43          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:12:43          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:12:43          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:12:43          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:12:43          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:12:43          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:12:43          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:12:43          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:12:43          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:12:43          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:12:43          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:12:43          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:12:43          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:12:43          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:12:44          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:12:44          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:12:44          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:12:44          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:12:44          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:12:44          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:12:44          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:12:44          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:12:44          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:12:44          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:12:44          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:12:44          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:12:44          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:12:44          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:12:44          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:12:44          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:12:44          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:12:44          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:12:45          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:12:45          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:12:45          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:12:45          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:12:45          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:12:45          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:12:45          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:12:45          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:12:45          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:12:45          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:12:45          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:12:45          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:12:45          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:12:45          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:12:45          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:12:45          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:12:45          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:12:45          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:12:46          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:12:46          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:12:46          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:12:46          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:12:46          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:12:46          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:12:46          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:12:46          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:12:46          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:12:46          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:12:46          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:12:46          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:12:46          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:12:46          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:12:46          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:12:46          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:12:46          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:12:46          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:12:47          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:12:47          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:12:47          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:12:47          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:12:47          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:12:47          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:12:47          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:12:47          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:12:47          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:12:47          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:12:47          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:12:47          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:12:47          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:12:47          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:12:47          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:12:47          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:12:47          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:12:47          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:12:48          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:12:48          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:12:48          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:12:48          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:12:48          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:12:48          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:12:48          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:12:48          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:12:48          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:12:48          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:12:48          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:12:48          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:12:48          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:12:48          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:12:48          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:12:48          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:12:48          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:12:48          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:12:49          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:12:49          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:12:49          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:12:49          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:12:49          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:12:49          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:12:49          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:12:49          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:12:49          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:12:49          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:12:49          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:12:49          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:12:49          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:12:49          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:12:49          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:12:49          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:12:49          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:12:49          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:12:50          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:12:50          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:12:50          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:12:50          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:12:50          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:12:50          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:12:50          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:12:50          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:12:50          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:12:50          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:12:50          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:12:50          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:12:50          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:12:50          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:12:50          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:12:50          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:12:50          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:12:50          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:12:51          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:12:51          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:12:51          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:12:51          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:12:51          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:12:51          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:12:51          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:12:51          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:12:51          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:12:51          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:12:51          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:12:51          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:12:51          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:12:51          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:12:51          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:12:51          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:12:51          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:12:51          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:12:52          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:12:52          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:12:52          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:12:52          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:12:52          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:12:52          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:12:52          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:12:52          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:12:52          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:12:52          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:12:52          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:12:52          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:12:52          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:12:52          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:12:52          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:12:52          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:12:52          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:12:52          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:12:53          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:12:53          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:12:53          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:12:53          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:12:53          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:12:53          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:12:53          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:12:53          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:12:53          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:12:53          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:12:53          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:12:53          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:12:53          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:12:53          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:12:53          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:12:53          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:12:53          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:12:53          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:12:54          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:12:54          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:12:54          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:12:54          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:12:54          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:12:54          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:12:54          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:12:54          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:12:54          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:12:54          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:12:54          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:12:54          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:12:54          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:12:54          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:12:54          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:12:54          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:12:54          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:12:54          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:12:55          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:12:55          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:12:55          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:12:55          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:12:55          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:12:55          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:12:55          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:12:55          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:12:55          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:12:55          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:12:55          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:12:55          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:12:55          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:12:55          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:12:55          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:12:55          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:12:55          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:12:55          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:12:56          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:12:56          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:12:56          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:12:56          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:12:56          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:12:56          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:12:56          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:12:56          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:12:56          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:12:56          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:12:56          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:12:56          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:12:56          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:12:56          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:12:56          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:12:56          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:12:56          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:12:56          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:12:57          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:12:57          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:12:57          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:12:57          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:12:57          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:12:57          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:12:57          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:12:57          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:12:57          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:12:57          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:12:57          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:12:57          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:12:57          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:12:57          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:12:57          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:12:57          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:12:57          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:12:57          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:12:58          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:12:58          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:12:58          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:12:58          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:12:58          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:12:58          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:12:58          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:12:58          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:12:58          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:12:58          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:12:58          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:12:58          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:12:58          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:12:58          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:12:58          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:12:58          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:12:58          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:12:58          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:12:59          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:12:59          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:12:59          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:12:59          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:12:59          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:12:59          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:12:59          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:12:59          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:12:59          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:12:59          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:12:59          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:12:59          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:12:59          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:12:59          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:12:59          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:12:59          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:12:59          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:12:59          server.py->line(162)|Client 16 join, total 18 clients
